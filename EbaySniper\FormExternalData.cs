using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using uBuyFirst.Views;
using System.Threading.Tasks;
using CefSharp.WinForms;
using System.Linq;
using uBuyFirst.CefBrowser;
using uBuyFirst.ExternalData;
using uBuyFirst.Other;
using uBuyFirst.Data;
using uBuyFirst.Item;
using uBuyFirst.SkuManager;
using DevExpress.XtraTab;
using IniParser;

namespace uBuyFirst
{
    public partial class FormExternalData : DevExpress.XtraBars.Ribbon.RibbonForm
    {
        public List<Keyword2Find> _eBaySearches;
        public bool IsInEditMode = false;
        public Process internalProcessHandle = null;

        private string? _pythonInstallPath = null;
        private string? _pythonVersion = null;
        private readonly CefBrowserManager _cefBrowserManager;
        Dictionary<string, List<string>> providersToModels = new Dictionary<string, List<string>>();
        Dictionary<int, string> dropDownToAvailableModels = new Dictionary<int, string>();
        private bool _isInitializing;
        private OpenFileDialog _openCsvDialog = new System.Windows.Forms.OpenFileDialog();
        // Use to help keep track of last state so when switching between AI and External and Internal 
        // can do the proper thing.
        private bool lastStateWasInternal = true;
        
        private void PopulateAiModelDictionarys()
        {
            if (providersToModels.Count == 0)
            {
                providersToModels.Add("OpenAI", new List<string>());
                providersToModels["OpenAI"].Add("openai/gpt-4o-mini");
                providersToModels["OpenAI"].Add("openai/o4-mini");
                providersToModels["OpenAI"].Add("openai/gpt-4.1-nano");
                providersToModels["OpenAI"].Add("openai/gpt-4.1-mini");
                providersToModels["OpenAI"].Add("openai/gpt-4.1");
                providersToModels.Add("Gemini", new List<string>());
                providersToModels["Gemini"].Add("gemini/gemini-2.0-flash-lite");
                providersToModels["Gemini"].Add("gemini/gemini-2.0-flash");
                providersToModels["Gemini"].Add("gemini/gemini-2.5-flash-preview-04-17");
                dropDownToAvailableModels.Add(0, "OpenAI");
                dropDownToAvailableModels.Add(1, "Gemini");

                // Add items to the dropdown menus for AI
                foreach (var kvp in providersToModels)
                {
                    aiProviderComboBox.Items.Add(kvp.Key);
                }
                foreach (var model in providersToModels[dropDownToAvailableModels[0]])
                {
                    modelComboBox.Items.Add(model);
                }

                // Optional: Set default selected item
                aiProviderComboBox.SelectedIndex = UserSettings.AiProviderIndex;
                modelComboBox.SelectedIndex = UserSettings.AiProviderModelIndex;
            }
        }

        public FormExternalData(CefBrowserManager cefBrowserManager)
        {
            InitializeComponent();
            _cefBrowserManager = cefBrowserManager;

            // Configure progress bar display format
            progressBarControlDownloadRequiredFiles.Properties.ShowTitle = true;
            progressBarControlDownloadRequiredFiles.Properties.DisplayFormat.FormatString = "Downloading Chrome {0}%";
            progressBarControlDownloadRequiredFiles.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Custom;

            UpdateUiState();
            chkExternalDataEnabled.Checked = UserSettings.ExternalDataEnabled;
            this.connectionTypeHorStackPanel.SetStretched(lblSpace, true);
            // this.externalDataHorStackPanel.SetStretched(lblSpace2, true);
            //Folders.SkuManagerScripts;
        }

        private bool FilesExistForEditMode()
        {
            // Make sure all files exist for edit mode before doing edit mode.
            string sFile = Path.Combine(Folders.SkuManagerScripts, SkuPath.Scripts.InternalSkuScript);
            string senderFile = Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.SenderFields);
            string senderCatFile = Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.SenderCategoryFields);
            string uniqueFieldFile = Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.UniqueFields);
            string csvScriptFile = Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.CsvScriptPath);
            if (File.Exists(sFile) && File.Exists(senderFile) && File.Exists(senderCatFile)
                && File.Exists(uniqueFieldFile) && File.Exists(csvScriptFile))
            {
                return true;
            }
            return false;
        }

        private void ChangeToNewScript()
        {
            newScriptButton.Show();
            applyScriptButton.Show();
            IsInEditMode = false;
            // Have to enable control to clear it
            matchingScriptEdit.Enabled = true;
            matchingTemplateEdit.Enabled = true;
            matchingScriptEdit.Clear();
            matchingTemplateEdit.Clear();
            matchingScriptEdit.Enabled = false;
            matchingTemplateEdit.Enabled = false;

            // Set tab control to page 1
            scriptStepsTabControl1.SelectedTabPageIndex = 0;
            SaveAndContCsvButton.Text = "Save and Continue";
            SaveAndContFieldSenderButton.Text = "Save and Continue";
            SaveAndContMatchScriptButton.Text = "Save and Continue";
            SaveAndContTemplateButton.Text = "Save and Continue";

            EnableStep1();
            DisableStep2();
            DisableStep3();
            DisableStep4();

            applyScriptButton.Enabled = true;
            startScriptButton.Enabled = true;
            applyScriptButton.Text = "Save All";
            startScriptButton.Text = "Start Script";
            applyScriptButton.Enabled = false;
            startScriptButton.Enabled = false;

            DisableApply();
            DisableSaveScript();

        }

        private void ReEnableEdit()
        {
            // scriptStepsTabControl1.Show();
            // Don't want to re-read from files when info already on screen
            // this enables to go back to applying stuff after running scrips.
            EnableStep3();
            EnableStep4();
            // Set tab control to page 3
            scriptStepsTabControl1.SelectedTabPageIndex = 2;
            IsInEditMode = true;
            SaveAndContMatchScriptButton.Text = "Save";
            SaveAndContTemplateButton.Text = "Save";
            DisableApply();
            DisableSaveScript();

        }

        private void ChangeToEditMode()
        {
            newScriptButton.Show();
            applyScriptButton.Show();
            // scriptStepsTabControl1.Show();
            PopulateFieldsForStep1FromExisting();
            PopulateFieldsForStep2FromExisting();
            DisableStep1();
            DisableStep2();
            EnableStep3();
            EnableStep4();
            populateMatchAndDisplayFromExistingScript();
            // Set tab control to page 3
            scriptStepsTabControl1.SelectedTabPageIndex = 2;
            IsInEditMode = true;
            SaveAndContMatchScriptButton.Text = "Save";
            SaveAndContTemplateButton.Text = "Save";
            DisableApply();
            DisableSaveScript();
        }

        private void ChangeToExternal()
        {
            // scriptStepsTabControl1.Show();
            HideTabsForExternal();
            MakeExternalVisible();
            ToggleDescriptionExternal();
            HideInternal();
            txtTemplateUrl.Enabled = true;
            UserSettings.IsInternalEndpoint = false;
            UserSettings.IsAIEndpoint = false;
            txtTemplateUrl.Text = UserSettings.ExternalEndpointUrl;
        }

        private void ChangeToAI()
        {
            // If coming from InternalEndpoint ensure buttons have the last size correct
            if (UserSettings.IsInternalEndpoint)
            {
                lastStateWasInternal = true;
            } 
            else
            {
                lastStateWasInternal = false;
            }
            newScriptButton.Hide();
            applyScriptButton.Hide();
            startScriptButton.Text = "Start Script";
            if (startScriptButtonSizeWhenOn.Width > 0)
            {
                startScriptButton.Size = startScriptButtonSizeWhenOn;
            }
            startScriptButton.Show();
            startScriptButton.Enabled = true;
            stopScriptButton.Show();
            stopScriptButton.Size = stopScriptButtonSize;
            stopScriptButton.Enabled = true;

            HideTabsForAI();
            MakeAIVisible();
            ToggleDescriptionExternal();
            HideInternal();
            txtTemplateUrl.Enabled = true;
            PopulateAiModelDictionarys();
            FillInAiPrompt();
            FillInDisplayTemplate();
            PopulateAIKey();
            UserSettings.IsInternalEndpoint = false;
            UserSettings.IsAIEndpoint = true;
            txtTemplateUrl.Text = UserSettings.ExternalEndpointUrl;
        }


        private void ExternalDataForm_Load(object sender, EventArgs e)
        {
            string scriptsFolder = Folders.SkuManagerScripts;
            txtTemplateUrl.Text = UserSettings.ExternalEndpointUrl;
            _isInitializing = true; // Prevent the CheckedChanged event from firing
            chkSendDescriptionAndPictures.Checked = UserSettings.SendDescriptionAndPictures;
            _isInitializing = false; // Allow event to fire after initialization
            var allowedColumns = GetAllowedColumns();
            var categorySpecificList = GetCategorySpecificColumns();
            // Remove Category specific items, they will be added in a seperate list.
            foreach (var categorySpec in categorySpecificList)
            {
                allowedColumns.Remove(categorySpec);
            }
            chkListUrlParamsCategories.Items.AddRange(categorySpecificList.ToArray());
            chkListUrlParams.Items.AddRange(allowedColumns.ToArray());
            UpdateCheckListBoxItems(allowedColumns);
            if (UserSettings.IsInternalEndpoint)
            {
                // This is not work with Internal but does work with AI so ensuring this setting when switching.
                UserSettings.SendDescriptionAndPictures = false;
                // This button should only show up on AI.
                stopScriptButton.Visible = false;
                if (stopScriptButton.Size.Width > 0)
                {
                    stopScriptButtonSize = stopScriptButton.Size;
                }
                stopScriptButton.Size = new System.Drawing.Size(0, 0);
                radioExternalDataGroups.SelectedIndex = 0;
                txtTemplateUrl.Text = UserSettings.InternalEndpointUrl;
                UserSettings.IsAIEndpoint = false;
                bool bfilesExistForEdit = FilesExistForEditMode();
                ShowTabsForInternal();
                ToggleDescriptionInternal();
                // If new script start at the beginning.
                if (!bfilesExistForEdit)
                {
                    // When opening need to enter the steps in order so disable other steps.
                    ChangeToNewScript();
                }
                else
                {
                    RestoreInternalScriptIfExists();
                    ChangeToEditMode();
                }

            }
            else if (UserSettings.IsAIEndpoint)
            {
                // This is required for AI for it to work.
                UserSettings.SendDescriptionAndPictures = true;
                ChangeToAI();
                radioExternalDataGroups.SelectedIndex = 2;
                PopulateAiModelDictionarys();
                FillInAiPrompt();
                FillInDisplayTemplate();
                PopulateAIKey();
            }
            else
            {
                // This button should only show up on AI.
                stopScriptButton.Visible = false;
                if (stopScriptButton.Size.Width > 0)
                {
                    stopScriptButtonSize = stopScriptButton.Size;
                }
                stopScriptButton.Size = new System.Drawing.Size(0, 0);
                ChangeToExternal();
                radioExternalDataGroups.SelectedIndex = 1;
            }

            // PopulateAiModelDictionarys();
            // TODO: Remove when get logic right.
            // FillInAiPrompt();
            // FillInDisplayTemplate();
            // PopulateAIKey();
        }

        private void UpdateUiState()
        {
            var filesMissing = _cefBrowserManager.AreRequiredFilesMissing();
            if (filesMissing)
            {
                btnDownloadRequiredFiles.Visible = true;
                btnManageChromeProfile.Visible = false;
                progressBarControlDownloadRequiredFiles.Visible = false;
            }
            else
            {
                btnDownloadRequiredFiles.Visible = false;
                btnManageChromeProfile.Visible = true;
                progressBarControlDownloadRequiredFiles.Visible = false;
            }
        }

        private async Task DownloadAndInitializeCefBrowser()
        {
            // If initialization fails or files are missing and can't be downloaded, return early.
            var filesMissing = _cefBrowserManager.AreRequiredFilesMissing();
            if (filesMissing)
            {

                btnDownloadRequiredFiles.Visible = false;
                progressBarControlDownloadRequiredFiles.Visible = true;

                var downloadSuccess = await _cefBrowserManager.DownloadCefFiles(UpdateDownloadProgressBar);
                if (downloadSuccess)
                {
                    AssemblyResolver.Initialize(Folders.CefSharpBinaries);
                    progressBarControlDownloadRequiredFiles.Visible = false;
                    btnManageChromeProfile.Visible = true;
                }
                else
                {
                    MessageBox.Show("Failed to initialize Cef environment.");
                    return;
                }
            }

            _cefBrowserManager.InitializeCefEngine();


            if (!panelCefBrowser.Controls.OfType<ChromiumWebBrowser>().Any())
            {
                var browser = _cefBrowserManager.CreateBrowser();
                panelCefBrowser.Controls.Add(browser);
                browser.Dock = DockStyle.Fill;
            }
            panelCefBrowser.Visible = true;
        }

        private void UpdateDownloadProgressBar(int progress)
        {
            if (progressBarControlDownloadRequiredFiles.InvokeRequired)
            {
                progressBarControlDownloadRequiredFiles.Invoke((Action)(() => progressBarControlDownloadRequiredFiles.EditValue = progress));
            }
            else
            {
                progressBarControlDownloadRequiredFiles.EditValue = progress;
            }
        }

        private async void btnDownloadFiles_Click(object sender, EventArgs e)
        {
            await DownloadAndInitializeCefBrowser();
        }

        private async void btnPreview_Click(object sender, EventArgs e)
        {
            btnPreview.Enabled = false;
            var (row, _) = Helpers.GetRandomRow(_eBaySearches);
            if (row != null)
            {
                var url = ExternalDataManager.BuildExternalDataUrl(txtTemplateUrl.Text, row);
                txtPreviewFinalUrl.Text = url;
                if (!string.IsNullOrEmpty(url))
                {
                    await DownloadAndInitializeCefBrowser();

                    var existingBrowser = panelCefBrowser.Controls.OfType<ChromiumWebBrowser>().FirstOrDefault();
                    if (existingBrowser == null)
                    {
                        existingBrowser = _cefBrowserManager.CreateBrowser();
                        panelCefBrowser.Controls.Add(existingBrowser);
                        existingBrowser.Dock = DockStyle.Fill;
                    }

                    ExternalDataManager.SendRequestWithData(existingBrowser, url, row);
                }
            }
            else
            {
                // Show confirmation dialog before starting search
                var dialogResult = XtraMessageBox.Show("Do you want to start the search to get results?",
                    "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (dialogResult == DialogResult.Yes)
                {
                    // Call the BtnMainStartClick method in Form1
                    Form1.Instance.BtnMainStartClick();
                }
                else
                {
                    XtraMessageBox.Show("Please click the Start button to retrieve search results first.");
                    // User chose not to proceed with the search
                    btnPreview.Enabled = true;
                    return;
                }
            }
            btnPreview.Enabled = true;
        }

        private void UpdateUrlBasedOnFields()
        {
            string finalEndpointUrl = UserSettings.InternalEndpointUrl;
            foreach (var checkUrlItem in chkListUrlParams.Items)
            {
                var checkItem = ((CheckedListBoxItem)checkUrlItem).Value.ToString().Replace(" ", "");
                var checkItemObject = ((CheckedListBoxItem)checkUrlItem);
                var paramExistsTemplate1 = $@"[?][^=&]+={{{Regex.Escape(checkItem)}}}";
                var paramExistsTemplate2 = $@"[&][^=&]+={{{Regex.Escape(checkItem)}}}";
                var paramToAddTemplate = $"{checkItem}={{{checkItem}}}";
                if (checkItemObject.CheckState == CheckState.Checked)
                {
                    var exists = Regex.IsMatch(finalEndpointUrl, paramExistsTemplate1)
                                    || Regex.IsMatch(finalEndpointUrl, paramExistsTemplate2);

                    if (!exists)
                    {
                        if (finalEndpointUrl.Contains("?"))
                            finalEndpointUrl = $"{finalEndpointUrl}&{paramToAddTemplate}";
                        else
                            finalEndpointUrl = $"{finalEndpointUrl}?{paramToAddTemplate}";
                    }
                }
                else
                {
                    finalEndpointUrl = Regex.Replace(finalEndpointUrl, $@"[?][^=&]+={{{Regex.Escape(checkItem)}}}&", "?", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    finalEndpointUrl = Regex.Replace(finalEndpointUrl, paramExistsTemplate1, "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    finalEndpointUrl = Regex.Replace(finalEndpointUrl, paramExistsTemplate2, "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }

            }
            UserSettings.InternalEndpointUrl = finalEndpointUrl;
            txtTemplateUrl.Text = UserSettings.InternalEndpointUrl;
        }


        private void chkListUrlParams_ItemCheck(object sender, DevExpress.XtraEditors.Controls.ItemCheckEventArgs e)
        {
            if (radioExternalDataGroups.SelectedIndex != 0)
            {
                var checkItem = chkListUrlParams.Items[e.Index].Value.ToString().Replace(" ", "");
                var paramExistsTemplate1 = $@"[?][^=&]+={{{Regex.Escape(checkItem)}}}";
                var paramExistsTemplate2 = $@"[&][^=&]+={{{Regex.Escape(checkItem)}}}";
                var paramToAddTemplate = $"{checkItem}={{{checkItem}}}";

                string finalEndpointUrl = (radioExternalDataGroups.SelectedIndex == 1 || radioExternalDataGroups.SelectedIndex == 2) ? UserSettings.ExternalEndpointUrl : UserSettings.InternalEndpointUrl;
                if (e.State == CheckState.Checked)
                {
                    var exists = Regex.IsMatch(finalEndpointUrl, paramExistsTemplate1)
                                    || Regex.IsMatch(finalEndpointUrl, paramExistsTemplate2);

                    if (!exists)
                    {
                        if (finalEndpointUrl.Contains("?"))
                            finalEndpointUrl = $"{finalEndpointUrl}&{paramToAddTemplate}";
                        else
                            finalEndpointUrl = $"{finalEndpointUrl}?{paramToAddTemplate}";
                    }
                }
                else
                {
                    finalEndpointUrl = Regex.Replace(finalEndpointUrl, $@"[?][^=&]+={{{Regex.Escape(checkItem)}}}&", "?", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    finalEndpointUrl = Regex.Replace(finalEndpointUrl, paramExistsTemplate1, "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    finalEndpointUrl = Regex.Replace(finalEndpointUrl, paramExistsTemplate2, "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }

                UserSettings.ExternalEndpointUrl = finalEndpointUrl;
                txtTemplateUrl.Text = UserSettings.ExternalEndpointUrl;
            }
        }

        private void txtTemplateUrl_EditValueChanged(object sender, EventArgs e)
        {
            if (radioExternalDataGroups.SelectedIndex == 1 || radioExternalDataGroups.SelectedIndex == 2)
            {
                UserSettings.ExternalEndpointUrl = txtTemplateUrl.Text;
                var (row, _) = Helpers.GetRandomRow(_eBaySearches);
                if (row == null) return;

                txtPreviewFinalUrl.Text = ExternalDataManager.BuildExternalDataUrl(txtTemplateUrl.Text, row);
                UpdateCheckListBoxItems(GetAllowedColumns());
            }
            else
            {
                // Can't modify url with internal;
            }
        }

        private void UpdateCheckListBoxItems(IEnumerable<string> allowedColumns)
        {
            foreach (var allowedColumn in allowedColumns)
            {
                var columnNameNoWhitespace = allowedColumn.Replace(" ", "");
                var columnNamePlaceholder = "{" + columnNameNoWhitespace + "}";
                var isChecked = UserSettings.ExternalEndpointUrl.Contains(columnNamePlaceholder) ? CheckState.Checked : CheckState.Unchecked;

                foreach (CheckedListBoxItem item in chkListUrlParams.Items)
                {
                    if (item.Value.ToString().Replace(" ", "") == columnNameNoWhitespace)
                    {
                        item.CheckState = isChecked;
                    }
                }
            }
        }

        public static List<string> GetAllowedColumns()
        {
            var columnList = new List<string>();
            foreach (var gridControl in ResultsView.ViewsDict.Values)
            {
                foreach (DataColumn column in ((DataTable)gridControl.DataSource).Columns)
                {
                    if (!columnList.Contains(column.Caption))
                        columnList.Add(column.Caption);
                }
            }

            columnList.Sort();
            columnList.Remove("ItemID");
            columnList.Remove("Description");
            columnList.Remove("Page Views");
            columnList.Remove("Blob");
            columnList.Remove("Source");
            columnList.Remove("Thumbnail");
            columnList.Remove("Relist Parent ID");

            return columnList;
        }

        public static List<string> GetCategorySpecificColumns()
        {
            var columnList = new List<string>();
            var catItems = ItemSpecifics.CategorySpecificsList;
            foreach (var category in catItems)
            {
                columnList.Add(category.CategoryName);
            }
            return columnList;
        }

        private void btnManageChromeProfile_Click(object sender, EventArgs e)
        {
            var profileFolder = Path.Combine(Folders.CefSharpRootCache, SkuPath.CefSharp.Profiles);
            var startInfo = new ProcessStartInfo
            {
                FileName = "chrome",  // Chrome executable
                Arguments = $" --user-data-dir={profileFolder} --disable-chrome-login-prompt --no-first-run --no-default-browser-check --disable-features=ProfilePickerOnStartup",  // Joining params with spaces
                UseShellExecute = true  // This runs the process without opening a shell window
            };

            // Start the process
            var process = new Process { StartInfo = startInfo };
            process.Start();
        }

        private void chkSendDescriptionAndPictures_CheckedChanged(object sender, EventArgs e)
        {
            UserSettings.SendDescriptionAndPictures = chkSendDescriptionAndPictures.Checked;
            if (_isInitializing) return; // Ignore event if the form is still loading
            if (chkSendDescriptionAndPictures.Checked)
            {
                XtraMessageBox.Show("The data will be sent via an HTTP POST request.");
            }
            else
            {
                XtraMessageBox.Show("The data will be sent via an HTTP GET request.");
            }
        }
        private void chkExternalDataEnabled_CheckedChanged(object sender, EventArgs e)
        {
            UserSettings.ExternalDataEnabled = chkExternalDataEnabled.Checked;
        }

        private void ToggleExternal(bool toggle)
        {
            // External has all the same fields as internal, internal just has more fields.
            if (toggle)
            {
                lblStep2Local.Text = "Fields To Send";
                chkListUrlParams.Enabled = true;
                chkListUrlParamsCategories.Enabled = true;
            }

        }

        System.Drawing.Size lblStep1LocalSize;
        System.Drawing.Size browseCsvStackPanelSize;
        System.Drawing.Size csvstackPanelMatchingScriptSize;
        System.Drawing.Size SaveAndContCsvButtonSize;
        System.Drawing.Size applyScriptButtonSize;
        System.Drawing.Size startScriptButtonSize;
        System.Drawing.Size startScriptButtonSizeWhenOn;
        System.Drawing.Size SaveAndContFieldSenderButtonSize;
        System.Drawing.Size stopScriptButtonSize;
        System.Drawing.Size lblTemplateScriptSize;
        System.Drawing.Size matchingTemplateEditSize;
        System.Drawing.Size SaveAndContTemplateButtonSize;
        System.Drawing.Size chkSendDescriptionAndPicturesSize;
        System.Drawing.Size newScriptButtonSize;
        bool bSizeForDescriptionSetFromInternal = false;

        private void ToggleDescriptionInternal()
        {
            chkSendDescriptionAndPicturesSize = chkSendDescriptionAndPictures.Size;
            chkSendDescriptionAndPictures.Size = new System.Drawing.Size(0, 0);
            bSizeForDescriptionSetFromInternal = true;
        }

        private void ToggleDescriptionExternal()
        {
            if (bSizeForDescriptionSetFromInternal)
            {
                chkSendDescriptionAndPictures.Size = chkSendDescriptionAndPicturesSize;
            } 
            else
            {
                chkSendDescriptionAndPictures.Size = new System.Drawing.Size(200, 19);
            }
        }

        private void ToggleInternal(bool toggle)
        {
            installPythonLinkButton.Visible = toggle;
            installPythonLibsButton.Visible = toggle;
            //lblPythonInstructions1.Visible = toggle;
            //lblPythonInstructions2.Visible = toggle;
            if (toggle)
            {
                lblStep2Local.Text = "Step 2: Fields To Send";
            }
            lblCsvFile.Visible = toggle;
            txtCsvFileName.Visible = toggle;
            browseButtonCsv.Visible = toggle;
            lblCsvFieldsToMatch.Visible = toggle;
            csvFieldsToUseForMatchListBoxControl.Visible = toggle;
            lblStep1Local.Visible = toggle;
            lblMatchingScript.Visible = toggle;
            matchingScriptEdit.Visible = toggle;
            SaveAndContMatchScriptButton.Visible = toggle;
            SaveAndContCsvButton.Visible = toggle;
            applyScriptButton.Visible = toggle;
            startScriptButton.Visible = toggle;
            // This button should only show up on AI.
            stopScriptButton.Visible = false;
            if (stopScriptButton.Size.Width > 0)
            {
                stopScriptButtonSize = stopScriptButton.Size;
            }
            stopScriptButton.Size = new System.Drawing.Size(0, 0);
            newScriptButton.Visible = toggle;
            SaveAndContFieldSenderButton.Visible = toggle;
            lblTemplateScript.Visible = toggle;
            matchingTemplateEdit.Visible = toggle;
            SaveAndContTemplateButton.Visible = toggle;

            // Code above enables the buttons.
            // Ensure coming from AI that we don't get the wrong size for components.
            // Internal -> AI -> Internal shouldn't change size of buttons.
            // External -> AI -> External shouldn't change size of buttons.
            // hence the return below to avoid changing the sizes.
            if ((toggle && lastStateWasInternal) || (!toggle && !lastStateWasInternal))
            {
                return;
            }
            // NOTE OF other cases
            // if (!toggle && lastStateWasInternal) This is okay because going from internal -> AI -> external so size will be right.
            // if (toggle && !lastStateWasInternal) This is okay because going external -> AI -> internal so size will be right

            if (toggle == false)
            {
                lblStep1LocalSize = lblStep1Local.Size;
                browseCsvStackPanelSize = browseCsvStackPanel.Size;
                csvstackPanelMatchingScriptSize = csvstackPanelMatchingScript.Size;
                SaveAndContCsvButtonSize = SaveAndContCsvButton.Size;
                applyScriptButtonSize = applyScriptButton.Size;
                startScriptButtonSize = startScriptButton.Size;
                startScriptButtonSizeWhenOn = startScriptButton.Size;
                newScriptButtonSize = newScriptButton.Size;
                SaveAndContFieldSenderButtonSize = SaveAndContFieldSenderButton.Size;
                lblTemplateScriptSize = lblTemplateScript.Size;
                matchingTemplateEditSize = matchingTemplateEdit.Size;
                SaveAndContTemplateButtonSize = SaveAndContTemplateButton.Size;
                lblStep1Local.Size = new System.Drawing.Size(0, 0);
                browseCsvStackPanel.Size = new System.Drawing.Size(0, 0);
                csvstackPanelMatchingScript.Size = new System.Drawing.Size(0, 0);
                SaveAndContCsvButton.Size = new System.Drawing.Size(0, 0);
                applyScriptButton.Size = new System.Drawing.Size(0, 0);
                startScriptButton.Size = new System.Drawing.Size(0, 0);
                newScriptButton.Size = new System.Drawing.Size(0, 0);
                SaveAndContFieldSenderButton.Size = new System.Drawing.Size(0, 0);
                lblTemplateScript.Size = new System.Drawing.Size(0, 0);
                matchingTemplateEdit.Size = new System.Drawing.Size(0, 0);
                SaveAndContTemplateButton.Size = new System.Drawing.Size(0, 0);
                lastStateWasInternal = false;
            }
            else
            {
                lblStep1Local.Size = lblStep1LocalSize;
                browseCsvStackPanel.Size = browseCsvStackPanelSize;
                csvstackPanelMatchingScript.Size = csvstackPanelMatchingScriptSize;
                SaveAndContCsvButton.Size = SaveAndContCsvButtonSize;
                applyScriptButton.Size = applyScriptButtonSize;
                startScriptButton.Size = startScriptButtonSize;
                newScriptButton.Size = newScriptButtonSize;
                SaveAndContFieldSenderButton.Size = SaveAndContFieldSenderButtonSize;
                lblTemplateScript.Size = lblTemplateScriptSize;
                matchingTemplateEdit.Size = matchingTemplateEditSize;
                SaveAndContTemplateButton.Size = SaveAndContTemplateButtonSize;
                lastStateWasInternal = true;
            }

        }

        private void MakeExternalVisible()
        {
            ToggleExternal(true);
        }

        private void MakeAIVisible()
        {
            ToggleExternal(true);
        }

        private void MakeInternalVisible()
        {
            ToggleInternal(true);
        }

        private void HideInternal()
        {
            ToggleInternal(false);
        }

        private void HideExternal()
        {
            ToggleExternal(false);
        }

        private void HideTabsForExternal()
        {
            scriptStepsTabControl1.TabPages[4].PageVisible = false;
            scriptStepsTabControl1.TabPages[5].PageVisible = false;
            scriptStepsTabControl1.TabPages[1].PageVisible = true;
            // Set tab control to page 2
            scriptStepsTabControl1.SelectedTabPageIndex = 1;
            // Hide other pages.
            var tabPages = scriptStepsTabControl1.TabPages;
            foreach (var tab in tabPages)
            {
                var tabPage = (XtraTabPage)tab;
                if (tabPage.Name != "scriptStepsTabPage2")
                {
                    tabPage.Hide();
                }
                else
                {
                    tabPage.Text = "Select Fields to send";
                }
            }
            scriptStepsTabControl1.TabPages[0].PageVisible = false;
            scriptStepsTabControl1.TabPages[2].PageVisible = false;
            scriptStepsTabControl1.TabPages[3].PageVisible = false;

        }

        private void HideTabsForAI()
        {
            scriptStepsTabControl1.TabPages[4].PageVisible = true;
            scriptStepsTabControl1.TabPages[5].PageVisible = true;
            // scriptStepsTabControl1.Hide();
            // Set tab control to page 5
            scriptStepsTabControl1.SelectedTabPageIndex = 4;
            // Hide other pages.
            var tabPages = scriptStepsTabControl1.TabPages;
            foreach (var tab in tabPages)
            {
                var tabPage = (XtraTabPage)tab;
                if ((tabPage.Name != "scriptAITabPage") &&
                    (tabPage.Name != "scriptAIDisplayPage"))
                {
                    tabPage.Hide();
                }
            }
            scriptStepsTabControl1.TabPages[0].PageVisible = false;
            scriptStepsTabControl1.TabPages[1].PageVisible = false;
            scriptStepsTabControl1.TabPages[2].PageVisible = false;
            scriptStepsTabControl1.TabPages[3].PageVisible = false;
        }



        private void ShowTabsForInternal()
        {
            scriptStepsTabControl1.TabPages[4].PageVisible = false;
            scriptStepsTabControl1.TabPages[5].PageVisible = false;
            scriptStepsTabControl1.TabPages[0].PageVisible = true;
            scriptStepsTabControl1.TabPages[1].PageVisible = true;
            scriptStepsTabControl1.TabPages[2].PageVisible = true;
            scriptStepsTabControl1.TabPages[3].PageVisible = true;

            var tabPages = scriptStepsTabControl1.TabPages;
            foreach (var tab in tabPages)
            {
                var tabPage = (XtraTabPage)tab;
                if (tabPage.Name != "scriptStepsTabPage2")
                {
                    tabPage.Show();
                }
                else
                {
                    tabPage.Show();
                    tabPage.Text = "Step 2";
                }
            }
            // Set tab control to page 3
            scriptStepsTabControl1.SelectedTabPageIndex = 3;
            // Hide other pages.
        }

        private void radioExternalDataGroups_SelectedIndexChanged(object sender, EventArgs e)
        {
            RadioGroup edit = sender as RadioGroup;
            if (edit.SelectedIndex == 0)
            {
                // This is not work with Internal but does work with AI so ensuring this setting when switching.
                UserSettings.SendDescriptionAndPictures = false;
                ShowTabsForInternal();
                MakeInternalVisible();
                ToggleDescriptionInternal();
                HideExternal();
                txtTemplateUrl.Enabled = false;
                txtTemplateUrl.Text = UserSettings.InternalEndpointUrl;
                UserSettings.IsAIEndpoint = false;
                bool bfilesExistForEdit = FilesExistForEditMode();
                // If new script start at the beginning.
                if (!bfilesExistForEdit)
                {
                    // When opening need to enter the steps in order so disable other steps.
                    ChangeToNewScript();
                } 
                else
                {
                    RestoreInternalScriptIfExists();
                    ChangeToEditMode();
                }
                UserSettings.IsInternalEndpoint = true;
            }
            else if (edit.SelectedIndex == 1)
            {
                ChangeToExternal();
            }
            else
            {
                // This is required for AI for it to work.
                UserSettings.SendDescriptionAndPictures = true;
                // Do these 2 so when switched back to internal can go back to editing that was started
                BackupInternalScriptIfExists();
                RestoreAISkuScript();
                // Change to AI
                ChangeToAI();
                PopulateAiModelDictionarys();
                FillInAiPrompt();
                FillInDisplayTemplate();
                PopulateAIKey();
            }
        }
                



        private void ValidateOnTextChange(object sender, EventArgs e)
        {

        }

        private void populateFromCsvFile(string fileLocation)
        {
            csvFieldsToUseForMatchListBoxControl.Items.Clear();
            var columns = new List<string>();
            using (var reader = new CsvFileReader(fileLocation))
            {
                var i = 0;
                while (reader.ReadRow(columns))
                {
                    if (i == 0)
                    {
                        // Only need to read the headers for this code.
                        csvFieldsToUseForMatchListBoxControl.Items.AddRange(columns.ToArray());
                        break;
                    }

                    i++;
                }
            }
            var CategorySpecificList = ItemSpecifics.CategorySpecificsList;
            var CurrentOnes = GetAllowedColumns();
        }

        private void browseButtonCsv_Click(object sender, EventArgs e)
        {
            _openCsvDialog.Filter = @"Comma separated values (*.csv)|*.csv";
            _openCsvDialog.InitialDirectory = Folders.SkuManagerScripts;
            _openCsvDialog.FileName = "";
            if (_openCsvDialog.ShowDialog() != DialogResult.OK)
                return;
            //var extDataCsvLog = "Skipped rows:\n";
            try
            {
                var fileLocation = _openCsvDialog.FileName;
                txtCsvFileName.Text = fileLocation;
                populateFromCsvFile(fileLocation);
            }
            catch (IOException ex)
            {
                XtraMessageBox.Show("Please, close csv you are trying to import and try again.\n" + ex.Message);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Importing csv file for external data: ", ex);
            }
        }

        private void lblMatchingScript_Click(object sender, EventArgs e)
        {

        }

        private void ToggleStep1(bool bValue)
        {
            txtCsvFileName.Enabled = bValue;
            browseButtonCsv.Enabled = bValue;
            csvFieldsToUseForMatchListBoxControl.Enabled = bValue;
            SaveAndContCsvButton.Enabled = bValue;
        }

        private void ToggleStep2(bool bValue)
        {
            chkListUrlParams.Enabled = bValue;
            chkListUrlParamsCategories.Enabled = bValue;
            SaveAndContFieldSenderButton.Enabled = bValue;
        }

        private void ToggleStep3(bool bValue)
        {
            matchingScriptEdit.Enabled = bValue;
            SaveAndContMatchScriptButton.Enabled = bValue;
        }

        private void ToggleStep4(bool bValue)
        {
            matchingTemplateEdit.Enabled = bValue;
            SaveAndContTemplateButton.Enabled = bValue;
        }

        private void DisableStep1()
        {
            ToggleStep1(false);
        }

        private void EnableStep1()
        {
            ToggleStep1(true);
        }

        private void DisableStep2()
        {
            ToggleStep2(false);
        }

        private void EnableStep2()
        {
            ToggleStep2(true);
        }

        private void DisableStep3()
        {
            ToggleStep3(false);
        }

        private void EnableStep3()
        {
            ToggleStep3(true);
        }

        private void DisableStep4()
        {
            ToggleStep4(false);
        }

        private void EnableStep4()
        {
            ToggleStep4(true);
        }

        private void ToggleApply(bool bValue)
        {
            applyScriptButton.Enabled = bValue;
        }

        private void EnableApply()
        {
            ToggleApply(true);
        }

        private void DisableApply()
        {
            ToggleApply(false);
        }

        private void ToggleSaveScript(bool bValue)
        {
            startScriptButton.Enabled = bValue;
        }

        private void EnableSaveScript()
        {
            ToggleSaveScript(true);
        }

        private void DisableSaveScript()
        {
            ToggleSaveScript(false);
        }

        private void SaveAndContCsvButton_Click(object sender, EventArgs e)
        {
            string csvFile = txtCsvFileName.Text;
            bool bHaveCsv = !string.IsNullOrEmpty(csvFile);
            if (!bHaveCsv)
            {
                MessageBox.Show("A valid csv file is required for completion of this step.", "Need a valid csv");
                return;
            }

            var fileName = Path.GetFileName(csvFile);
            var extension = Path.GetExtension(csvFile);
            if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(extension) ||
                (extension.ToLower() != ".csv"))
            {
                MessageBox.Show("A valid csv file is required for completion of this step.", "Need a valid csv");
                return;
            }

            List<string> uniqueFields = new List<string>();
            // write Files to file
            foreach (CheckedListBoxItem field in csvFieldsToUseForMatchListBoxControl.Items)
            {
                if (field.CheckState == CheckState.Checked)
                {
                    uniqueFields.Add(field.Value.ToString().Replace(" ", ""));
                }
            }
            if (uniqueFields.Count <= 0)
            {
                MessageBox.Show("Must select at least one field to use with the matching script to complete this step.");
                return;
            }

            // Write Csv Part for script
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.CsvScriptPart)))
            {
                // Escape backslashes for Python string literal and use Python raw string (r"...")
                string fullPathForPython = csvFile.Replace(@"\", @"\\");
                string csvLine = $"csv_name_from_client = pd.read_csv(r\"{fullPathForPython}\")";
                outputFile.WriteLine(csvLine);
            }

            // Write Csv Path so can be read later by the edit.
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.CsvScriptPath)))
            {
                string csvPathLine = csvFile;
                outputFile.WriteLine(csvPathLine);
            }

            // write unique fields to file.
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.UniqueFields)))
            {
                foreach (var field in uniqueFields)
                {
                    outputFile.WriteLine(field);
                }
            }

            // Make fields readonly for step 1, enable next step
            DisableStep1();
            EnableStep2();
            scriptStepsTabControl1.SelectedTabPageIndex = 1;

            SaveAndContCsvButton.Text = "Csv Info Saved";
        }

        private void UpdateListBoxControl(CheckedListBoxControl control, string fileName)
        {
            HashSet<string> senderFields = new HashSet<string>();
            using (StreamReader inputStreamReader = new StreamReader(Path.Combine(Folders.SkuManagerScripts, fileName)))
            {
                string fields = inputStreamReader.ReadLine();
                while (fields != null)
                {
                    senderFields.Add(fields.Trim('\r').Trim('\n').Replace(" ", ""));
                    fields = inputStreamReader.ReadLine();
                }
            }
            foreach (CheckedListBoxItem field in control.Items)
            {
                var strValue = field.Value.ToString().Replace(" ", "");
                if (senderFields.Contains(strValue))
                {
                    field.CheckState = CheckState.Checked;
                }
                else
                {
                    field.CheckState = CheckState.Unchecked;
                }
            }
        }

        private void PopulateFieldsForStep2FromExisting()
        {
            EnableStep2();
            UpdateListBoxControl(chkListUrlParams, SkuPath.Configuration.SenderFields);
            UpdateListBoxControl(chkListUrlParamsCategories, SkuPath.Configuration.SenderCategoryFields);
            DisableStep2();
        }

        private void PopulateFieldsForStep1FromExisting()
        {
            EnableStep1();
            using (StreamReader inputStreamReader = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.CsvScriptPath)))
            {
                string fields = inputStreamReader.ReadLine();
                txtCsvFileName.Text = fields;
            }
            populateFromCsvFile(txtCsvFileName.Text);
            UpdateListBoxControl(csvFieldsToUseForMatchListBoxControl, SkuPath.Configuration.UniqueFields);
            DisableStep1();
        }
        
        private void SaveAndContFieldSenderButton_Click(object sender, EventArgs e)
        {
            List<string> fields = new List<string>();
            // write unique fields to file.
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.SenderFields)))
            {
                foreach (CheckedListBoxItem field in chkListUrlParams.Items)
                {
                    if (field.CheckState == CheckState.Checked)
                    {
                        fields.Add(field.Value.ToString().Replace(" ", ""));
                        outputFile.WriteLine(field);
                    }
                }
            }

            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.SenderCategoryFields)))
            {
                foreach (CheckedListBoxItem field in chkListUrlParamsCategories.Items)
                {
                    if (field.CheckState == CheckState.Checked)
                    {
                        fields.Add(field.Value.ToString().Replace(" ", ""));
                        outputFile.WriteLine(field);
                    }
                }
            }
            // write fields needed to call service to a file.
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.RequestCallFields)))
            {
                outputFile.WriteLine("    request: Request,");
                int count = fields.Count;
                int i = 0;
                foreach (var field in fields)
                {
                    string lineToWrite = "    " + field.ToLower() + ": str = Query(None, alias = \"" + field + "\"" + "),";
                    // remove last comma
                    if (i == count - 1)
                    {
                        lineToWrite = lineToWrite.Remove(lineToWrite.Length - 1, 1);
                    }
                    outputFile.WriteLine(lineToWrite);
                    i++;
                }

            }

            List<string> keys = new List<string>();
            using (StreamReader streamReader = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.UniqueFields)))
            {
                string line = streamReader.ReadLine();
                while (line != null)
                {
                    keys.Add(line);
                    line = streamReader.ReadLine();
                }
            }

            int queryNum = 1;
            foreach (string key in keys)
            {
                string match = "target_string" + queryNum + " = " + "\" \"" + ".join([";
                int count = fields.Count;
                int i = 0;
                foreach (var field in fields)
                {
                    match += field.ToLower() + " or '', ";
                    // remove last comma, add final statement.
                    if (i == count - 1)
                    {
                        match = match.Remove(match.Length - 2, 2);
                        match += "]).lower()";
                        // Add line to step 3 so user can edit it.
                        matchingScriptEdit.AppendLine(match);
                    }
                    i++;
                }
                queryNum++;
            }
            queryNum = 1;
            foreach (string key in keys)
            {
                string match = "matching_rows" + queryNum + " = " + "csv_name_from_client[csv_name_from_client[\"";
                match += key + "\"" + "].apply(lambda " + key.ToLower() + ": " + key.ToLower() + ".lower() in target_string" + queryNum + ")]";
                // Add line to step 3 so user can edit it.
                matchingScriptEdit.AppendLine(match);
                queryNum++;
            }
            /*
            target_string1 = " ".join([title or '', model or '', mpn or '', product_reference_id or '', upc or '']).lower()
            target_string2 = " ".join([title or '', model or '', mpn or '', product_reference_id or '', upc or '']).lower()
            matching_rows1 = csv_name_from_client[csv_name_from_client["SKU"].apply(lambda sku: sku.lower() in target_string1)]
            matching_rows2 = csv_name_from_client[csv_name_from_client["SKU"].apply(lambda sku: sku.lower() in target_string2)]
            */

            // Update url ?Model={Model}&MPN={MPN}&UPC={UPC}&Title={Title}&Brand={Brand}&ProductReferenceID={ProductReferenceID}
            // http://localhost:8000/match_mydata

            // Make fields readonly for step 2, enable next step
            DisableStep2();
            EnableStep3();
            scriptStepsTabControl1.SelectedTabPageIndex = 2;

            SaveAndContFieldSenderButton.Text = "Fields to Send Saved";
        }

        private void populateMatchAndDisplayFromExistingScript()
        {
            matchingScriptEdit.Clear();
            matchingTemplateEdit.Clear();
            using (StreamReader streamReader = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Scripts.InternalSkuScript)))
            {
                string line = streamReader.ReadLine();
                while (line != null)
                {
                    if (line.Contains("    #### Start of MatchingScript ####"))
                    {
                        line = streamReader.ReadLine();
                        while (!line.Contains("    #### End of MatchingScript ####"))
                        {
                            var lineToWrite = RemoveIndentation(line);
                            matchingScriptEdit.AppendLine(lineToWrite);
                            line = streamReader.ReadLine();
                        }

                    }
                    else if (line.Contains("    #### Start of DisplayTemplate ####"))
                    {
                        line = streamReader.ReadLine();
                        while (!line.Contains("    #### End of DisplayTemplate ####"))
                        {
                            var lineToWrite = RemoveIndentation(line);
                            matchingTemplateEdit.AppendLine(lineToWrite);
                            line = streamReader.ReadLine();
                        }
                    }
                    line = streamReader.ReadLine();
                }
            }
        }

        private void applyScriptButton_Click(object sender, EventArgs e)
        {
            if (System.IO.File.Exists(Folders.SkuManagerScripts + "\\" + SkuPath.Scripts.InternalSkuScript))
            {
                // rename current file so there is a backup.
                System.IO.File.Copy(Folders.SkuManagerScripts + "\\" + SkuPath.Scripts.InternalSkuScript, Folders.SkuManagerScripts + "\\internalSkuScript_Backup" + DateTime.Now.Ticks + ".py");
                System.IO.File.Delete(Folders.SkuManagerScripts + "\\" + SkuPath.Scripts.InternalSkuScript);
            }

            using (StreamWriter streamWriter = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Scripts.InternalSkuScript)))
            {
                using (StreamReader streamReader = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Scripts.InternalSkuScriptTemplate)))
                {
                    string line = streamReader.ReadLine();
                    while (line != null)
                    {
                        bool bHadTransformations = false;
                        // Perform transformations
                        if (line.Contains("csv_name_from_client = pd.read_csv(\"ReplaceWithClientName0001.csv\")"))
                        {
                            using (StreamReader streamReaderCsv = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.CsvScriptPart)))
                            {
                                line = streamReaderCsv.ReadLine();
                                streamWriter.WriteLine(line);
                                bHadTransformations = true;
                            }
                        }
                        else if (line.Contains("    #### Replace with Query FIELDS ####"))
                        {
                            using (StreamReader streamReaderCallFields = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Configuration.RequestCallFields)))
                            {
                                line = streamReaderCallFields.ReadLine();
                                while (line != null)
                                {
                                    streamWriter.WriteLine(line);
                                    bHadTransformations = true;
                                    line = streamReaderCallFields.ReadLine();
                                }
                            }
                        }
                        else if (line.Contains("    #### Start of MatchingScript ####"))
                        {
                            // Erase lines 
                            while (!line.Contains("    #### End of MatchingScript ####"))
                            {
                                line = streamReader.ReadLine();
                            }
                            using (StreamReader streamReaderMatchingScript = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Components.MatchScript)))
                            {
                                streamWriter.WriteLine("    #### Start of MatchingScript ####");
                                line = streamReaderMatchingScript.ReadLine();
                                while (line != null)
                                {
                                    streamWriter.WriteLine(line);
                                    bHadTransformations = true;
                                    line = streamReaderMatchingScript.ReadLine();
                                }
                                streamWriter.WriteLine("    #### End of MatchingScript ####");
                            }

                        }
                        else if (line.Contains("    #### Replace with MatchingScript ####"))
                        {
                            using (StreamReader streamReaderMatchingScript = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Components.MatchScript)))
                            {
                                streamWriter.WriteLine("    #### Start of MatchingScript ####");
                                line = streamReaderMatchingScript.ReadLine();
                                while (line != null)
                                {
                                    streamWriter.WriteLine(line);
                                    bHadTransformations = true;
                                    line = streamReaderMatchingScript.ReadLine();
                                }
                                streamWriter.WriteLine("    #### End of MatchingScript ####");
                            }
                        }
                        else if (line.Contains("    #### Start of DisplayTemplate ####"))
                        {
                            // Erase lines 
                            while (!line.Contains("    #### End of DisplayTemplate ####"))
                            {
                                line = streamReader.ReadLine();
                            }
                            using (StreamReader streamReaderMatchingScript = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Components.DisplayTemplateScript)))
                            {
                                streamWriter.WriteLine("    #### Start of DisplayTemplate ####");
                                line = streamReaderMatchingScript.ReadLine();
                                while (line != null)
                                {
                                    streamWriter.WriteLine(line);
                                    bHadTransformations = true;
                                    line = streamReaderMatchingScript.ReadLine();
                                }
                                streamWriter.WriteLine("    #### End of DisplayTemplate ####");
                            }

                        }
                        else if (line.Contains("    #### Replace with DisplayTemplate ####"))
                        {
                            using (StreamReader streamReaderMatchingScript = new StreamReader(Path.Combine(Folders.SkuManagerScripts, SkuPath.Components.DisplayTemplateScript)))
                            {
                                streamWriter.WriteLine("    #### Start of DisplayTemplate ####");
                                line = streamReaderMatchingScript.ReadLine();
                                while (line != null)
                                {
                                    streamWriter.WriteLine(line);
                                    bHadTransformations = true;
                                    line = streamReaderMatchingScript.ReadLine();
                                }
                                streamWriter.WriteLine("    #### End of DisplayTemplate ####");
                            }
                        }


                        // Writeline back same line
                        if (!bHadTransformations)
                        {
                            streamWriter.WriteLine(line);
                        }

                        line = streamReader.ReadLine();

                    }
                }
            }
            DisableApply();
            // After you have applied don't want the user to think the edit mode of the step 3 and 4 would apply.
            // After they start the script, steps 3 and 4 would be re-enabled.
            if (IsInEditMode)
            {
                DisableStep3();
                DisableStep4();
            }
            EnableSaveScript();
            applyScriptButton.Text = "Script Created!";
        }

        private void newScriptButton_Click(object sender, EventArgs e)
        {
            ChangeToNewScript();
        }

        private void UpdateButtonsOnTemplateSaved()
        {
            if (IsInEditMode)
            {
                applyScriptButton.Text = "Save All";
                startScriptButton.Text = "Start Script";
            }
        }


        private void SaveAndContMatchScriptButton_Click(object sender, EventArgs e)
        {
            var lines = matchingScriptEdit.Lines;
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Components.MatchScript)))
            {
                foreach (var line in lines)
                {
                    string lineToWrite = AddIndentation(line);
                    outputFile.WriteLine(lineToWrite);
                }
            }

            bool bfilesExistForEdit = FilesExistForEditMode();
            // If new script make readonly and move to the next step
            if ((!bfilesExistForEdit) || (!IsInEditMode))
            {
                // Make fields readonly for step 3
                DisableStep3();
                EnableStep4();
                matchingTemplateEdit.AppendText(GetBaseOutputTemplate());
            }
            // If change text and in edit mode ensure buttons to save and run have appropriate text.
            UpdateButtonsOnTemplateSaved();
            // Don't want to go to the next tab in edit mode.
            if (!IsInEditMode)
            {
                scriptStepsTabControl1.SelectedTabPageIndex = 3;
            }
            // After changing either match script or template script should be able to save.
            if (IsInEditMode)
            {
                EnableApply(); 
            }
            SaveAndContMatchScriptButton.Text = "Match Script Saved";
        }

        private string AddIndentation(string line)
        {
            string lineToWrite = line;
            if (line.Length > 0)
            {
                // Add indentation to front of lines.
                lineToWrite = "    " + line;
            }
            return lineToWrite;
        }

        private string RemoveIndentation(string line)
        {
            string lineToWrite = line;
            if (line.Length > 0)
            {
                if (lineToWrite.StartsWith("    "))
                {
                    lineToWrite = lineToWrite.Remove(0, 4);
                }
            }
            return lineToWrite;
        }

        private void SaveAndContTemplateButton_Click(object sender, EventArgs e)
        {
            var lines = matchingTemplateEdit.Lines;
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(Folders.SkuManagerScripts, SkuPath.Components.DisplayTemplateScript)))
            {
                foreach (var line in lines)
                {
                    string lineToWrite = AddIndentation(line);
                    outputFile.WriteLine(lineToWrite);
                }
            }
            bool bfilesExistForEdit = FilesExistForEditMode();
            // If new script make readonly and move to the next step
            if ((!bfilesExistForEdit) || (!IsInEditMode))
            {
                // Make fields readonly for step 4
                DisableStep4();
            }
            EnableApply();
            // If change text and in edit mode ensure buttons to save and run have appropriate text.
            UpdateButtonsOnTemplateSaved();

            SaveAndContTemplateButton.Text = "Display Template Saved";
        }


        private void startScriptButton_Click(object sender, EventArgs e)
        {
            DisableSaveScript();
            // Don't modify internal url for AI endpoint.
            if (!UserSettings.IsAIEndpoint)
            {
                UpdateUrlBasedOnFields();
            }
            PythonProcessManager.StopInternalSkuManagerScript();
            var processId = PythonProcessManager.StartInternalSkuManagerScript();
            if (processId > 0)
            {
                // If Script is running then enable txtTemplateUrl
                txtTemplateUrl.Enabled = true;
                startScriptButton.Text = "Script Started!";
                ReEnableEdit();
            }
            else
            {
                MessageBox.Show("Script Failed to start.  Please check that port 8000 is available.  Script runs from http://localhost:8000/", "Script failed to start");
            }
        }

        private void lblCsvFieldsToMatch_Click(object sender, EventArgs e)
        {

        }

        private void csvAllstackPanel_Paint(object sender, PaintEventArgs e)
        {

        }

        private string GetBaseOutputTemplate()
        {
            StringBuilder outPutTemplate = new StringBuilder();
            outPutTemplate.AppendLine("# Convert to HTML table");
            outPutTemplate.AppendLine("table = \"\"\"<table style=\"border-collapse: collapse; width: 80%; margin: auto; text-align: left;\">\"\"\"");
            outPutTemplate.AppendLine("table += f\"\"\"");
            outPutTemplate.AppendLine("    <thead>");
            outPutTemplate.AppendLine("        <tr style=\"background-color: #f2f2f2;\">");
            outPutTemplate.AppendLine("\"\"\"");
            outPutTemplate.AppendLine("");
            outPutTemplate.AppendLine("for attr in matching_rows['columns']:");
            outPutTemplate.AppendLine("    table += f\"\"\"<th style=\"padding: 8px; border: 1px solid black;\">{attr}</th>\"\"\"");
            outPutTemplate.AppendLine("");
            outPutTemplate.AppendLine("table += \"\"\"");
            outPutTemplate.AppendLine("        </tr>");
            outPutTemplate.AppendLine("    </thead>");
            outPutTemplate.AppendLine("    <tbody>");
            outPutTemplate.AppendLine("\"\"\"");
            outPutTemplate.AppendLine("");
            outPutTemplate.AppendLine("for row in matching_rows['data']:");
            outPutTemplate.AppendLine("    table += \"\"\"<tr>\"\"\"");
            outPutTemplate.AppendLine("    for value in row:");
            outPutTemplate.AppendLine("        table += f\"\"\"");
            outPutTemplate.AppendLine("            <td style=\"padding: 8px; border: 1px solid black;\">{value if value is not None else 'N/A'}</td>");
            outPutTemplate.AppendLine("        \"\"\"");
            outPutTemplate.AppendLine("    table += \"\"\"</tr>\"\"\"");
            outPutTemplate.AppendLine("table += \"</tbody></table>\"");
            outPutTemplate.AppendLine("");
            return outPutTemplate.ToString();
        }

        private void layoutControl1_Resize(object sender, EventArgs e)
        {
            // layoutControl1.Width = this.ClientSize.Width - 230;
            // layoutControl1.Height = this.ClientSize.Height - 646;
        }

        private void FormExternalData_Resize(object sender, EventArgs e)
        {
            // this.Width = this.ClientSize.Width;
            // this.Height = this.ClientSize.Height;
        }

        double layoutTopPer = 0.15;
        double layoutMidPer = 0.35;
        double layoutBotPer = 0.50;
        int offsetTop = 38;
        int offsetWidth = 5;

        private void layoutControl1_Resize_1(object sender, EventArgs e)
        {
            // layoutControl1.Width = this.ClientSize.Width - offsetWidth;
            // layoutControl1.Height = (int)((double)(this.ClientSize.Height - offsetTop) * layoutMidPer);
            // This is second control based of off first one
            // layoutControl1.Location = new System.Drawing.Point(0, offsetTop + (int)((double)this.ClientSize.Height * layoutTopPer) + 10);
        }

        private void layoutControl2_Resize(object sender, EventArgs e)
        {
            // layoutControl2.Width = this.ClientSize.Width - offsetWidth;
            // layoutControl2.Height = (int)((double)(this.ClientSize.Height - offsetTop) * layoutTopPer);
            // layoutControl2.Location = new System.Drawing.Point(0, offsetTop);
        }

        private void layoutControl3_Resize(object sender, EventArgs e)
        {
            // connectionTypeStackPanel.Width = this.ClientSize.Width - offsetWidth;
            // connectionTypeStackPanel.Height = (int)((double)(this.ClientSize.Height - offsetTop) * layoutTopPer);
            // connectionTypeStackPanel.Location = new System.Drawing.Point(0, offsetTop);

            // csvAllstackPanel.Width = this.ClientSize.Width - offsetWidth;
            // csvAllstackPanel.Height = (int)((double)(this.ClientSize.Height-offsetTop) * layoutMidPer);
            // This is second control based of off first one
            // csvAllstackPanel.Location = new System.Drawing.Point(0, offsetTop + (int)((double)this.ClientSize.Height * layoutTopPer) +10);

            // previewStackPanel.Width = this.ClientSize.Width - offsetWidth;
            // previewStackPanel.Height = (int)((double)(this.ClientSize.Height - offsetTop) * layoutBotPer) - offsetTop;
            // previewStackPanel.Location = new System.Drawing.Point(0, offsetTop + (int)((double)this.ClientSize.Height * (layoutTopPer + layoutMidPer)) + 20);
        }


        private void installPythonLinkButton_Click(object sender, EventArgs e)
        {
            var startInfo = new ProcessStartInfo();
            startInfo.WorkingDirectory = Folders.SkuManagerScripts;
            startInfo.CreateNoWindow = false;
            startInfo.UseShellExecute = true;
            startInfo.FileName = SkuPath.Scripts.PythonInstaller;
            startInfo.Arguments = "PrependPath=1 Include_test=0";
            startInfo.WindowStyle = ProcessWindowStyle.Normal;

            try
            {
                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    process.WaitForExit(); // Wait for the installer to finish
                }
                else
                {
                    MessageBox.Show("Failed to start the Python installer process.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred while running the Python installer: {ex.Message}", "Installer Exception", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Attempts to find the highest installed Python version (3.x+) installation path
        /// from the Windows Registry.
        /// </summary>
        /// <returns>A tuple containing the installation path and version string, or (null, null) if not found.</returns>
        private (string? Path, string? Version) FindHighestPythonInstallPath()
        {
            // List of versions to check, from newest to oldest (down to 3.x)
            var versionsToCheck = new List<string> { "3.12" };

            foreach (var version in versionsToCheck)
            {
                try
                {
                    string? installPath = null;
                    // Try HKEY_CURRENT_USER first
                    var currentUserRegPath = $@"Software\Python\PythonCore\{version}\InstallPath";
                    using (var key = Registry.CurrentUser.OpenSubKey(currentUserRegPath))
                    {
                        if (key != null)
                        {
                            installPath = key.GetValue(null)?.ToString(); // Default value is often the install path
                            if (!string.IsNullOrEmpty(installPath) && Directory.Exists(installPath))
                            {
                                return (installPath, version); // Found in HKCU
                            }
                        }
                    }

                    // Try HKEY_LOCAL_MACHINE if not found in HKCU
                    var localMachineRegPath = $@"SOFTWARE\Python\PythonCore\{version}\InstallPath";
                    using (var key = Registry.LocalMachine.OpenSubKey(localMachineRegPath))
                    {
                        if (key != null)
                        {
                            installPath = key.GetValue(null)?.ToString(); // Default value
                            if (!string.IsNullOrEmpty(installPath) && Directory.Exists(installPath))
                            {
                                return (installPath, version); // Found in HKLM
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception for this specific version check if necessary
                    Console.WriteLine($@"Error accessing registry for Python {version}: {ex.Message}");
                    // Continue checking other versions
                }
            }

            // If no suitable version was found after checking all specified versions
            return (null, null);
        }
        private void installPythonLibsButton_Click(object sender, EventArgs e)
        {
            // Find the highest installed Python version (3.+)
            var (detectedPath, detectedVersion) = FindHighestPythonInstallPath();
            _pythonInstallPath = detectedPath; // Store for use later in the method
            _pythonVersion = detectedVersion; // Store the detected version

            if (string.IsNullOrEmpty(_pythonInstallPath))
            {
                MessageBox.Show("Python 3.12 or higher installation path not detected. Please install Python using the 'Install Python' button first, or ensure a compatible Python version (3.12) is installed and registered correctly.", "Python Path Missing", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var startInfo = new ProcessStartInfo();
            startInfo.WorkingDirectory = Folders.SkuManagerScripts;
            startInfo.CreateNoWindow = false;
            startInfo.UseShellExecute = true;
            startInfo.FileName = Path.Combine(startInfo.WorkingDirectory, SkuPath.Scripts.SetupScriptItems);
            startInfo.WindowStyle = ProcessWindowStyle.Normal;

            try
            {
                using var process = new Process();
                process.StartInfo = startInfo;
                process.Start();
                process.WaitForExit(); // Wait for the batch script to finish

            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred while running the setup script: {ex.Message}", "Script Exception", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void connectionTypeStackPanel_Paint(object sender, PaintEventArgs e)
        {

        }

        private void scriptStepsTabControl1_SelectedPageChanged(object sender, TabPageChangedEventArgs e)
        {
            if (IsInEditMode)
            {
                if (e.Page != null)
                {
                    XtraTabPage selectPage = e.Page as XtraTabPage;
                    if (scriptStepsTabControl1.SelectedTabPageIndex == 2)
                    {
                        SaveAndContMatchScriptButton.Text = "Save";
                    }
                    else if (scriptStepsTabControl1.SelectedTabPageIndex == 3)
                    {
                        SaveAndContTemplateButton.Text = "Save";
                    }
                }
            }
        }

        private void aiProviderComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            System.Windows.Forms.ComboBox comboBox = (System.Windows.Forms.ComboBox)sender;

            modelComboBox.Items.Clear();
            int selectedAIModel = (int)comboBox.SelectedIndex;
            if (selectedAIModel >= 0)
            {
                // Save the settings so when we reload it will set it.
                UserSettings.AiProviderIndex = selectedAIModel;
                if (dropDownToAvailableModels.ContainsKey(selectedAIModel))
                {
                    foreach (var model in providersToModels[dropDownToAvailableModels[selectedAIModel]])
                    {
                        modelComboBox.Items.Add(model);
                    }
                    modelComboBox.SelectedIndex = 0;
                }
            }

        }

        private void WriteAIProviderKeys()
        {
            var folderPath = Folders.SkuManagerScripts;
            var envFile = Path.Combine(folderPath, SkuPath.Configuration.Environment);
            var parser = new FileIniDataParser();
            var iniData = parser.ReadFile(envFile);
            var config = iniData.Configuration;
            // Write API Key
            if (aiProviderComboBox.SelectedItem.ToString() == "OpenAI")
            {
                iniData["AICONFIG"]["OPENAI_API_KEY"] = "\"" + txtApiKey.Text + "\"";
            }
            else if (aiProviderComboBox.SelectedItem.ToString() == "Gemini")
            {
                iniData["AICONFIG"]["GEMINI_API_KEY"] = "\"" + txtApiKey.Text + "\"";
            }
            // Write Model
            iniData["AICONFIG"]["LITELLM_MODEL_STRING"] = "\"" + modelComboBox.SelectedItem.ToString() + "\"";

            parser.WriteFile(envFile, iniData);
        }

        private void PopulateAIKey()
        {
            var folderPath = Folders.SkuManagerScripts;
            var envFile = Path.Combine(folderPath, SkuPath.Configuration.Environment);
            var parser = new FileIniDataParser();
            var iniData = parser.ReadFile(envFile);
            var config = iniData.Configuration;
            if (aiProviderComboBox.SelectedItem.ToString() == "OpenAI")
            {
                txtApiKey.Text = iniData["AICONFIG"]["OPENAI_API_KEY"].Replace("\"", "");
            }
            else if (aiProviderComboBox.SelectedItem.ToString() == "Gemini")
            {
                txtApiKey.Text = iniData["AICONFIG"]["GEMINI_API_KEY"].Replace("\"", "");

            }
        }

        private void saveAIPrompt()
        {
            var folderPath = Folders.SkuManagerScripts;
            var envFile = Path.Combine(folderPath, SkuPath.Subfolders.Prompts);

            if (System.IO.File.Exists(envFile + "\\" + SkuPath.AI.SystemMessage))
            {
                // rename current file so there is a backup.
                System.IO.File.Copy(envFile + "\\" + SkuPath.AI.SystemMessage, envFile + "\\system_message_Backup" + DateTime.Now.Ticks + ".txt");
                System.IO.File.Delete(envFile + "\\" + SkuPath.AI.SystemMessage);
            }

            var lines = aiPromptEdit.Lines;
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(envFile, SkuPath.AI.SystemMessage)))
            {
                foreach (var line in lines)
                {
                    outputFile.WriteLine(line);
                }
            }

            // copy AI template over the internalSkuScript.py
            if (System.IO.File.Exists(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript))
            {
                // rename current file so there is a backup.
                System.IO.File.Copy(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript, folderPath + "\\internalSkuScript_Backup" + DateTime.Now.Ticks + ".py");
                System.IO.File.Delete(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript);
            }
            // Copy over template that will run the AI
            System.IO.File.Copy(folderPath + "\\" + SkuPath.Scripts.InternalSkuScriptAITemplate, folderPath + "\\" + SkuPath.Scripts.InternalSkuScript);


        }

        private void FillInAiPrompt()
        {
            var folderPath = Folders.SkuManagerScripts;
            var envFile = Path.Combine(folderPath, SkuPath.Subfolders.Prompts);

            aiPromptEdit.Clear();
            using (StreamReader inputStreamReader = new StreamReader(Path.Combine(envFile, SkuPath.AI.SystemMessage)))
            {
                string fields = inputStreamReader.ReadLine();
                while (fields != null)
                {
                    aiPromptEdit.AppendLine(fields);
                    fields = inputStreamReader.ReadLine();
                }
            }
        }


        private void saveAIConfigurationButton_Click(object sender, EventArgs e)
        {
            WriteAIProviderKeys();
            saveAIPrompt();

        }

        private void RestoreInternalScriptIfExists()
        {
            var folderPath = Folders.SkuManagerScripts;
            if (System.IO.File.Exists(folderPath + "\\" + SkuPath.Scripts.InternalSkuScriptInternal))
            {
                if (System.IO.File.Exists(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript))
                {
                    // rename current file so there is a backup.
                    System.IO.File.Copy(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript, folderPath + "\\internalSkuScript_Backup" + DateTime.Now.Ticks + ".py");
                    System.IO.File.Delete(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript);
                }

                System.IO.File.Copy(folderPath + "\\" + SkuPath.Scripts.InternalSkuScriptInternal, folderPath + "\\" + SkuPath.Scripts.InternalSkuScript);

            }
        }

        private void BackupInternalScriptIfExists()
        {
            if (UserSettings.IsInternalEndpoint)
            {
                var folderPath = Folders.SkuManagerScripts;
                // copy AI template over the internalSkuScript.py
                if (System.IO.File.Exists(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript))
                {
                    if (System.IO.File.Exists(folderPath + "\\" + SkuPath.Scripts.InternalSkuScriptInternal))
                    {
                        System.IO.File.Delete(folderPath + "\\" + SkuPath.Scripts.InternalSkuScriptInternal);
                    }
                    // rename current file so there is a backup.
                    System.IO.File.Copy(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript, folderPath + "\\" + SkuPath.Scripts.InternalSkuScriptInternal);
                }
            }
        }

        private void RestoreAISkuScript()
        {
            if (UserSettings.IsInternalEndpoint)
            {
                var folderPath = Folders.SkuManagerScripts;
                // copy AI template over the internalSkuScript.py
                if (System.IO.File.Exists(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript))
                {
                    // rename current file so there is a backup.
                    System.IO.File.Copy(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript, folderPath + "\\internalSkuScript_Backup" + DateTime.Now.Ticks + ".py");
                    System.IO.File.Delete(folderPath + "\\" + SkuPath.Scripts.InternalSkuScript);
                }
                // Copy over template that will run the AI
                System.IO.File.Copy(folderPath + "\\" + SkuPath.Scripts.InternalSkuScriptAITemplate, folderPath + "\\" + SkuPath.Scripts.InternalSkuScript);
            }
        }


        private void saveAIConfigurationDisplayButton_Click(object sender, EventArgs e)
        {
            var folderPath = Folders.SkuManagerScripts;
            var envFile = Path.Combine(folderPath, SkuPath.Subfolders.Templates);

            if (System.IO.File.Exists(envFile + "\\" + SkuPath.AI.AnalysisResult))
            {
                // rename current file so there is a backup.
                System.IO.File.Copy(envFile + "\\" + SkuPath.AI.AnalysisResult, envFile + "\\analysis_result_Backup" + DateTime.Now.Ticks + ".html");
                System.IO.File.Delete(envFile + "\\" + SkuPath.AI.AnalysisResult);
            }

            var lines = memoEditAIDisplayTemplate.Lines;
            using (StreamWriter outputFile = new StreamWriter(Path.Combine(envFile, SkuPath.AI.AnalysisResult)))
            {
                foreach (var line in lines)
                {
                    outputFile.WriteLine(line);
                }
            }
        }

        private void FillInDisplayTemplate()
        {
            var folderPath = Folders.SkuManagerScripts;
            var envFile = Path.Combine(folderPath, SkuPath.Subfolders.Templates);

            memoEditAIDisplayTemplate.Clear();
            using (StreamReader inputStreamReader = new StreamReader(Path.Combine(envFile, SkuPath.AI.AnalysisResult)))
            {
                string fields = inputStreamReader.ReadLine();
                while (fields != null)
                {
                    memoEditAIDisplayTemplate.AppendLine(fields);
                    fields = inputStreamReader.ReadLine();
                }
            }
        }

        private void modelComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            System.Windows.Forms.ComboBox comboBox = (System.Windows.Forms.ComboBox)sender;

            int selectedAIModel = (int)comboBox.SelectedIndex;
            if (selectedAIModel >= 0)
            {
                // Save the settings so when we reload it will set it.
                UserSettings.AiProviderModelIndex = selectedAIModel;
            }
        }

        private void stopScriptButton_Click(object sender, EventArgs e)
        {
            PythonProcessManager.StopInternalSkuManagerScript();
        }
    }
}
