# URL-Based AI Implementation

## Overview

The AI system has been updated to replace the Gemini AI service with a URL-based JSON fetching approach, similar to how the SKU manager works.

## Changes Made

### 1. New UrlJsonService.cs
- Created `EbaySniper/AI/UrlJsonService.cs`
- Fetches JSON responses from configurable URL endpoints
- Uses the same URL building pattern as `ExternalDataManager.BuildExternalDataUrl()`
- Supports placeholders like `{Title}`, `{ItemPrice}`, etc.
- Includes proper error handling and timeout management

### 2. Updated AiAnalysis.cs
- Replaced `GeminiService` with `UrlJsonService`
- Maintained the same JSON parsing and column population logic
- Preserved concurrency control (semaphore) and UI thread handling
- Updated debug messages to reflect URL-based approach

### 3. Updated FormExternalData.cs
- Modified `ChangeToAI()` to enable URL template editing in AI mode
- Updated `txtTemplateUrl_EditValueChanged()` to handle AI mode (index 2)
- Updated `chkListUrlParams_ItemCheck()` to support AI mode
- AI mode now uses `UserSettings.ExternalEndpointUrl` for configuration

### 4. Updated Form1.cs
- Removed test call to `GeminiService.SendHelloMessage()`
- Added explanatory comment about the replacement

### 5. Updated uBuyFirst.csproj
- Added `UrlJsonService.cs` to the compilation list

## How It Works

1. **URL Configuration**: In AI mode, users can configure a URL template in the same field used for External mode
2. **Parameter Substitution**: The URL template supports placeholders that get replaced with actual item data:
   - `{Title}` - Item title
   - `{ItemPrice}` - Item price
   - `{TotalPrice}` - Total price including shipping
   - `{Description}` - Item description
   - And many more (see `ExternalDataManager.GetRowValue()`)

3. **JSON Response**: The URL endpoint should return JSON in the same format as before:
   ```json
   {
     "Part Number Mismatch": "value1",
     "Door is missing": "value2",
     "Terminal block is missing": "value3",
     ...
   }
   ```

4. **Column Population**: JSON properties are automatically mapped to AI columns with "ai " prefix

## Example URL Templates

- `https://api.example.com/analyze?title={Title}&price={ItemPrice}`
- `https://myservice.com/ai/process?item={Title}&condition={Condition}`
- `https://localhost:8080/analyze?title={Title}&description={Description}`

## Benefits

- **Flexibility**: Any HTTP endpoint can provide AI responses
- **Consistency**: Uses the same URL building infrastructure as SKU manager
- **Maintainability**: Leverages existing, tested code patterns
- **Configuration**: Easy to configure different AI providers via URL
- **No API Keys**: No need for specific AI service API keys in the application

## Migration Notes

- Existing AI column definitions remain unchanged
- JSON response format stays the same
- All existing AI functionality is preserved
- Users need to configure a URL endpoint instead of API keys
