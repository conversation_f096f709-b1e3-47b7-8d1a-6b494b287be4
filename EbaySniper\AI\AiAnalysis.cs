﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.Utils.Menu;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using uBuyFirst.CustomClasses;
using uBuyFirst.Data;

namespace uBuyFirst.AI
{

    internal class AiAnalysis
    {
        public static CustomBindingList<string> AiColumnsList;
        public static async void UpdateAnalysis(object sender, EventArgs e)
        {
            try
            {
                var menuItem = sender as DXMenuItem;
                if (menuItem?.Tag is GridView gridView)
                {
                    if (gridView.GridControl.DataSource is DataTable dataTable)
                    {
                        // Create an instance of UrlJsonService
                        var urlJsonService = new UrlJsonService();
                        Debug.WriteLine("Starting URL-based AI analysis of product titles...");

                        var semaphore = new SemaphoreSlim(5); // limit concurrency to 5
                        var tasks = new List<Task>();
                        var uiContext = SynchronizationContext.Current;
                        foreach (DataRow row in dataTable.Rows)
                        {
                            // Wait until there is room in the semaphore.
                            await semaphore.WaitAsync();

                            // Launch a task to process this row.
                            tasks.Add(Task.Run(async () =>
                            {
                                try
                                {
                                    if (!row.RowState.HasFlag(DataRowState.Deleted) && !row.IsNull("Title"))
                                    {
                                        var d = (DataList)row["Blob"];

                                        var title = d.Title;
                                        if (!string.IsNullOrEmpty(title))
                                        {
                                            Debug.WriteLine($"Processing Title: {title}");

                                            // Fetch JSON response from URL endpoint
                                            var jsonResponse = await urlJsonService.FetchJsonFromUrl(row);
                                            jsonResponse = jsonResponse.Trim('`').Replace("json\n", "");
                                            dynamic json = JsonConvert.DeserializeObject(jsonResponse);

                                            // Convert dynamic json to a JObject for iteration.
                                            var jObject = json as JObject;
                                            var aiColumns = new List<KeyValuePair<string, string>>();

                                            if (jObject != null)
                                            {
                                                foreach (var property in jObject.Properties())
                                                {
                                                    aiColumns.Add(new KeyValuePair<string, string>(property.Name,
                                                        property?.Value?.ToString() ?? ""));
                                                }
                                            }

                                            uiContext.Post(_ =>
                                            {
                                                // This code runs on the UI thread.
                                                foreach (var responseKeyValue in aiColumns)
                                                {
                                                    if (row.Table.Columns.Contains("ai " + responseKeyValue.Key))
                                                    {
                                                        row["ai " + responseKeyValue.Key] = responseKeyValue.Value;
                                                    }
                                                }
                                            }, null);

                                            // Output the analysis to debugger
                                            Debug.WriteLine($"URL AI Analysis: {jsonResponse}");
                                            Debug.WriteLine("----------------------------------------");
                                        }
                                    }
                                }
                                finally
                                {
                                    // Always release the semaphore even if an exception occurs.
                                    semaphore.Release();
                                }
                            }));
                        }

                        // Wait for all tasks to complete.
                        await Task.WhenAll(tasks);
                        Debug.WriteLine($"Processed {dataTable.Rows.Count} items with URL-based AI");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateAnalysis: {ex.Message}");
                XtraMessageBox.Show($"Error processing grid data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void GetLargePictureUrls(List<string> picturesUrl)
        {
            const int ImageWidthHeight = 1600; // High resolution image size
            
            for (var i = 0; i < picturesUrl.Count; i++)
            {
                var url = picturesUrl[i];
                string imgID = null;
                
                // Try to pull ID from the "/z/<ID>/" form
                var match = System.Text.RegularExpressions.Regex.Match(url, @"/z/([^/]+)/");
                if (match.Success)
                {
                    imgID = match.Groups[1].Value;
                }
                else
                {
                    // Otherwise, maybe it's already in the "/images/g/<ID>/" form
                    match = System.Text.RegularExpressions.Regex.Match(url, @"/images/g/([^/]+)/");
                    if (match.Success)
                    {
                        imgID = match.Groups[1].Value;
                    }
                    else
                    {
                        // If no ID found, leave it unchanged
                        continue;
                    }
                }
                
                // Update the URL to the high-resolution format
                picturesUrl[i] = $"https://i.ebayimg.com/images/g/{imgID}/s-l{ImageWidthHeight}.webp";
            }
        }

        public static void AiColumnsList_ListChanged(object sender, ListChangedEventArgs e)
        {

        }

        public static void AiColumnsList_ItemDeleting(object sender, string e)
        {

        }
    }
}
