using System;
using System.Data;
using System.Net.Http;
using System.Threading.Tasks;
using uBuyFirst.ExternalData;
using uBuyFirst.Prefs;

namespace uBuyFirst.AI
{
    public class UrlJsonService
    {
        private readonly HttpClient _httpClient;

        public UrlJsonService()
        {
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(30)
            };
        }

        /// <summary>
        /// Fetches JSON response from a URL template using the same pattern as SKU manager
        /// </summary>
        /// <param name="row">DataRow containing the item data</param>
        /// <returns>JSON response string</returns>
        public async Task<string> FetchJsonFromUrl(DataRow row)
        {
            try
            {
                // Use the ExternalEndpointUrl when in AI mode, similar to how External mode works
                var urlTemplate = UserSettings.ExternalEndpointUrl;
                
                if (string.IsNullOrEmpty(urlTemplate))
                {
                    return "{\"error\": \"No URL template configured for AI endpoint\"}";
                }

                // Build URL with parameters using existing ExternalDataManager logic
                var finalUrl = ExternalDataManager.BuildExternalDataUrl(urlTemplate, row);
                
                if (string.IsNullOrEmpty(finalUrl))
                {
                    return "{\"error\": \"Failed to build URL from template\"}";
                }

                // Fetch JSON response
                var response = await _httpClient.GetAsync(finalUrl);
                response.EnsureSuccessStatusCode();
                
                var jsonContent = await response.Content.ReadAsStringAsync();
                
                // Return the JSON content directly
                return jsonContent;
            }
            catch (HttpRequestException ex)
            {
                return $"{{\"error\": \"HTTP request failed: {ex.Message}\"}}";
            }
            catch (TaskCanceledException ex)
            {
                return $"{{\"error\": \"Request timeout: {ex.Message}\"}}";
            }
            catch (Exception ex)
            {
                return $"{{\"error\": \"Error fetching from URL: {ex.Message}\"}}";
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
